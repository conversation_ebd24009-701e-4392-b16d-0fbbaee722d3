/* 重构内容区域样式 - 基于README.md主题色配置 */

/* 主题色变量 */
:root {
    --primary-color: #FFC300; /* Bright Yellow (Meituan-like) */
    --secondary-color: #FFDE59; /* Vibrant Lighter Yellow */
    --accent-color: #FF6B9D; /* Consistent accent */
    --warning-color: #FF6B6B;
    --success-color: #4ECDC4;
    --bright-yellow: #FFD166;
    --indigo-color: #4D5DFB;
    --mint-green: #06D6A0;

    --text-primary: #333333;
    --text-secondary: #666666;
    --text-hint: #999999;
    --background-color: #F8F9FA;
}

/* 重构内容显示区域 */
.refactored-content-display-area {
    padding: 20px 16px;
    background: var(--background-color);
}

/* 默认隐藏所有内容板块，通过JavaScript控制显示 */
.content-section {
    display: none;
}

/* 当对应的金刚功能区被激活时显示 */
.content-section.active {
    display: block;
}

/* 内容板块通用样式 */
.content-section.active {
    margin-bottom: 32px;
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

/* 板块标题区域 */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.section-header h2 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
}

/* 筛选按钮 */
.section-filters {
    display: flex;
    gap: 8px;
}

.filter-btn {
    padding: 6px 16px;
    border: 1px solid #e0e0e0;
    border-radius: 20px;
    background: white;
    color: var(--text-secondary);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn.active,
.filter-btn:hover {
    background: var(--primary-color);
    color: var(--text-primary); /* Ensure contrast on yellow */
    border-color: var(--primary-color);
}

/* 热门推荐横向滚动区域 */
.featured-companions,
.featured-activities,
.featured-tickets,
.featured-groups {
    margin-bottom: 24px;
}

.featured-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;
}

.companions-scroll,
.activities-scroll,
.tickets-scroll,
.groups-scroll {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding: 8px 0;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.companions-scroll::-webkit-scrollbar,
.activities-scroll::-webkit-scrollbar,
.tickets-scroll::-webkit-scrollbar,
.groups-scroll::-webkit-scrollbar {
    display: none;
}

/* 小卡片样式 */
.companion-mini-card,
.activity-mini-card,
.ticket-mini-card,
.group-mini-card {
    flex-shrink: 0;
    width: 120px;
    background: white;
    border-radius: 12px;
    padding: 12px;
    border: 1px solid #f0f0f0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.companion-mini-card:hover,
.activity-mini-card:hover,
.ticket-mini-card:hover,
.group-mini-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

/* 游戏玩伴小卡片 */
.companion-avatar-mini {
    position: relative;
    width: 60px;
    height: 60px;
    margin: 0 auto 8px;
}

.companion-avatar-mini img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    background: var(--success-color);
    border: 2px solid white;
    border-radius: 50%;
}

.online-indicator.offline {
    background: #ccc;
}

.rank-badge {
    position: absolute;
    top: -4px;
    left: 50%;
    transform: translateX(-50%);
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 600;
    color: white;
}

.rank-badge.king {
    background: linear-gradient(45deg, #FFD700, #FFA500);
}

.rank-badge.master {
    background: linear-gradient(45deg, #C0C0C0, #808080);
}

.rank-badge.pro {
    background: linear-gradient(45deg, #CD7F32, #8B4513);
}

.companion-mini-info h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px 0;
    text-align: center;
}

.game-type,
.activity-type,
.group-type {
    font-size: 12px;
    color: var(--text-secondary);
    text-align: center;
    display: block;
    margin-bottom: 4px;
}

.rating {
    font-size: 12px;
    color: var(--bright-yellow);
    text-align: center;
    margin-bottom: 4px;
}

.price {
    font-size: 14px;
    font-weight: 600;
    color: var(--warning-color);
    text-align: center;
}

.price span {
    font-size: 10px;
    font-weight: normal;
    color: var(--text-hint);
}

/* 活动小卡片 */
.activity-image-mini,
.ticket-image-mini,
.group-image-mini {
    position: relative;
    width: 100%;
    height: 60px;
    margin-bottom: 8px;
    border-radius: 8px;
    overflow: hidden;
}

.activity-image-mini img,
.ticket-image-mini img,
.group-image-mini img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.activity-tag,
.discount-badge,
.group-tag {
    position: absolute;
    top: 4px;
    right: 4px;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    color: white;
}

.activity-tag.hot,
.group-tag.hot {
    background: var(--warning-color);
}

.activity-tag.new,
.group-tag.new {
    background: var(--success-color);
}

.activity-tag {
    background: var(--primary-color);
}

.discount-badge {
    background: var(--warning-color);
}

.group-tag.urgent {
    background: var(--warning-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.activity-mini-info h4,
.ticket-mini-info h4,
.group-mini-info h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 4px 0;
}

.participants,
.members {
    font-size: 12px;
    color: var(--text-secondary);
}

/* 门票小卡片价格 */
.price-mini {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
}

.price-mini .current {
    font-size: 14px;
    font-weight: 600;
    color: var(--warning-color);
}

.price-mini .original {
    font-size: 12px;
    color: var(--text-hint);
    text-decoration: line-through;
}

/* 主要卡片容器 */
.cards-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 通用卡片样式 */
.content-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.content-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

/* 游戏玩伴卡片 */
.game-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.game-info {
    display: flex;
    gap: 12px;
    flex: 1;
}

.game-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
}

.game-details h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 8px 0;
}

.gender-tag {
    font-size: 14px;
    margin-left: 4px;
}

.gender-tag.female {
    color: #FF69B4;
}

.gender-tag.male {
    color: #4169E1;
}

.game-tags {
    display: flex;
    gap: 8px;
}

.game-tag,
.rank-tag {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.game-tag {
    background: var(--secondary-color);
    color: var(--text-primary);
}

.rank-tag.king {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: white;
}

.rank-tag.master {
    background: linear-gradient(45deg, #C0C0C0, #808080);
    color: white;
}

.rank-tag.pro {
    background: linear-gradient(45deg, #CD7F32, #8B4513);
    color: white;
}

.online-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.online-status.online .status-dot {
    background: var(--success-color);
}

.online-status.offline .status-dot {
    background: #ccc;
}

.online-status.online {
    color: var(--success-color);
}

.online-status.offline {
    color: var(--text-hint);
}

.game-card .card-body {
    padding: 16px;
}

.card-description {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: 12px;
}

.card-features {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
}

.feature {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: var(--text-secondary);
}

.feature i {
    color: var(--primary-color);
}

.card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.price-info {
    display: flex;
    align-items: baseline;
    gap: 4px;
}

.price-info .price {
    font-size: 20px;
    font-weight: 600;
    color: var(--warning-color);
}

.price-info .unit {
    font-size: 12px;
    color: var(--text-hint);
}

.card-cta-button {
    padding: 8px 20px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.card-cta-button:hover {
    background: var(--indigo-color);
    transform: translateY(-1px);
}

.card-cta-button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* 查看更多按钮 */
.load-more-section {
    text-align: center;
    margin-top: 24px;
}

.load-more-btn {
    padding: 12px 32px;
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    border-radius: 24px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.load-more-btn:hover {
    background: var(--primary-color);
    color: white;
}

/* 城市玩伴卡片样式 */
.city-activity-card .card-image-container {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.city-activity-card .card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.activity-status {
    position: absolute;
    top: 12px;
    right: 12px;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    color: white;
}

.status-badge.recruiting {
    background: var(--success-color);
}

.status-badge.hot {
    background: var(--warning-color);
}

.status-badge.full {
    background: #999;
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.activity-category {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: var(--secondary-color);
    border-radius: 12px;
    font-size: 12px;
    color: var(--text-primary);
}

.activity-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 16px;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--text-secondary);
}

.detail-item i {
    color: var(--primary-color);
    width: 12px;
}

.participants-preview {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.participants-avatars {
    display: flex;
    align-items: center;
    gap: -8px;
}

.participants-avatars img {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid white;
    margin-left: -8px;
}

.participants-avatars img:first-child {
    margin-left: 0;
}

.more-count {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--text-hint);
    color: white;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: -8px;
}

.participants-text {
    font-size: 12px;
    color: var(--text-secondary);
}

/* 景点门票卡片样式 */
.ticket-card .card-image-container {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.ticket-badges {
    position: absolute;
    top: 12px;
    left: 12px;
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.badge {
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 600;
    color: white;
}

.badge.hot {
    background: var(--warning-color);
}

.badge.new {
    background: var(--success-color);
}

.badge.discount {
    background: var(--warning-color);
}

.badge.nature {
    background: var(--mint-green);
}

.ticket-rating {
    position: absolute;
    top: 12px;
    right: 12px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.ticket-rating i {
    color: var(--bright-yellow);
}

.ticket-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.ticket-category {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: var(--secondary-color);
    border-radius: 12px;
    font-size: 12px;
    color: var(--text-primary);
}

.ticket-features {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: var(--text-secondary);
}

.feature-item i {
    color: var(--primary-color);
}

.ticket-details {
    margin-bottom: 16px;
}

.location-info,
.validity-info {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.location-info i,
.validity-info i {
    color: var(--primary-color);
    width: 12px;
}

.distance {
    color: var(--text-hint);
    margin-left: auto;
}

.price-section {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    margin-bottom: 16px;
}

.price-info {
    display: flex;
    align-items: baseline;
    gap: 8px;
}

.current-price {
    font-size: 24px;
    font-weight: 700;
    color: var(--warning-color);
}

.original-price {
    font-size: 14px;
    color: var(--text-hint);
    text-decoration: line-through;
}

.save-amount {
    font-size: 12px;
    color: var(--success-color);
    background: rgba(76, 205, 196, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
}

.unit-price {
    font-size: 12px;
    color: var(--text-hint);
}

.sales-info {
    text-align: right;
}

.sales-count {
    font-size: 12px;
    color: var(--text-hint);
}

/* 组局搭子卡片样式 */
.group-card .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.organizer-info {
    display: flex;
    gap: 12px;
    flex: 1;
}

.organizer-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.organizer-details h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 6px 0;
}

.organizer-tags {
    display: flex;
    gap: 6px;
}

.tag {
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 500;
}

.tag.verified {
    background: var(--success-color);
    color: white;
}

.tag.level {
    background: var(--primary-color);
    color: white;
}

.group-status {
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 600;
    color: white;
}

.group-status.urgent {
    background: var(--warning-color);
}

.group-status.hot {
    background: var(--warning-color);
}

.group-status.recruiting {
    background: var(--success-color);
}

.group-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.group-category {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 4px 8px;
    background: var(--secondary-color);
    border-radius: 12px;
    font-size: 12px;
    color: var(--text-primary);
}

.group-details {
    margin-bottom: 16px;
}

.detail-row {
    display: flex;
    gap: 16px;
    margin-bottom: 8px;
}

.detail-row:last-child {
    margin-bottom: 0;
}

.group-requirements {
    margin-bottom: 16px;
}

.group-requirements h5 {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0 0 8px 0;
}

.requirements-tags {
    display: flex;
    gap: 6px;
    flex-wrap: wrap;
}

.req-tag {
    padding: 4px 8px;
    background: #f0f0f0;
    border-radius: 12px;
    font-size: 11px;
    color: var(--text-secondary);
}

.members-preview {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
}

.members-avatars {
    display: flex;
    align-items: center;
}

.members-avatars img {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    border: 2px solid white;
    margin-left: -8px;
}

.members-avatars img:first-child {
    margin-left: 0;
}

.empty-slot {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background: #e0e0e0;
    color: var(--text-hint);
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: -8px;
    border: 2px solid white;
}

.members-text {
    font-size: 12px;
    color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .refactored-content-display-area {
        padding: 16px 12px;
    }

    .content-section {
        padding: 16px;
        margin-bottom: 24px;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .section-filters {
        width: 100%;
        justify-content: flex-start;
        overflow-x: auto;
        padding-bottom: 4px;
    }

    .filter-btn {
        flex-shrink: 0;
    }

    .card-features,
    .ticket-features {
        flex-direction: column;
        gap: 8px;
    }

    .card-footer,
    .price-section {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }

    .card-cta-button {
        width: 100%;
        padding: 12px;
    }

    .activity-details {
        grid-template-columns: 1fr;
    }

    .detail-row {
        flex-direction: column;
        gap: 8px;
    }

    .activity-header,
    .ticket-header,
    .group-header {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }

    .participants-preview,
    .members-preview {
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }

    .requirements-tags {
        justify-content: center;
    }
}
